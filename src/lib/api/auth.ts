import { User, LoginCredentials, RegisterCredentials } from "@/types/auth";
import { validatePassword } from "@/lib/utils/validation";

/**
 * Authentication API service
 * This file contains all the API calls related to authentication
 * Replace the dummy implementations with actual API calls when connecting to your backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Helper function to get stored token
const getStoredToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
};

// Helper function to store token
const storeToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', token);
  }
};

// Helper function to remove token and user data
const clearStoredData = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
  }
};

// Helper function to make authenticated requests
const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const token = getStoredToken();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string> || {})
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers,
  });
};

export const authService = {
  /**
   * Login user with credentials
   * @param credentials User login credentials
   * @returns Promise with user data
   */
  login: async (credentials: LoginCredentials): Promise<User> => {
    try {
      // Call login endpoint
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Login failed');
      }

      const tokenData = await response.json();
      
      // Store the token
      storeToken(tokenData.access_token);

      // Get user information using the token
      const userResponse = await makeAuthenticatedRequest('/auth/me');
      
      if (!userResponse.ok) {
        clearStoredData();
        throw new Error('Failed to get user information');
      }

      const userData = await userResponse.json();

      // Store user data for offline access
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(userData));
      }

      return userData;
    } catch (error) {
      clearStoredData();
      throw error;
    }
  },

  /**
   * Register new user
   * @param credentials User registration data
   * @returns Promise with created user data
   */
  register: async (credentials: RegisterCredentials): Promise<User> => {
    try {
      // Frontend validation (matching the existing validation)
      if (credentials.password !== credentials.confirmPassword) {
        throw new Error("Passwords do not match");
      }

      // Call register endpoint
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Registration failed');
      }

      const userData = await response.json();

      // After successful registration, log the user in to get a token
      const loginResponse = await authService.login({
        email: credentials.email,
        password: credentials.password,
      });

      return loginResponse;
    } catch (error) {
      clearStoredData();
      throw error;
    }
  },

  /**
   * Logout user
   * @returns Promise that resolves when logout is complete
   */
  logout: async (): Promise<void> => {
    try {
      // Clear stored data
      clearStoredData();
      
      // Note: FastAPI backend doesn't need a logout endpoint for JWT tokens
      // Tokens are stateless and expire automatically
      return Promise.resolve();
    } catch (error) {
      // Always clean up local storage even if there's an error
      clearStoredData();
      throw error;
    }
  },

  /**
   * Get current user
   * @returns Promise with user data or null if not authenticated
   */
  getCurrentUser: async (): Promise<User | null> => {
    try {
      const token = getStoredToken();
      if (!token) {
        return null;
      }

      const response = await makeAuthenticatedRequest('/auth/me');

      if (!response.ok) {
        // Token might be expired or invalid
        clearStoredData();
        return null;
      }

      const userData = await response.json();

      // Store user data for offline access
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(userData));
      }

      return userData;
    } catch (error) {
      console.error('Failed to get current user:', error);
      clearStoredData();
      return null;
    }
  },
};
